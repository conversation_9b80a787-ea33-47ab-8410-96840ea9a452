"""
Tests for the Brainstorming Module.

Comprehensive test suite covering all brainstorming functionality including
progressive questioning, pattern confidence scoring, and intelligent decisions.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock

from backend.src.modules.brainstorming import (
    BrainstormingSession,
    QuestionnaireResponse,
    PatternConfidence,
    IntelligentDecision,
    Question,
    QuestionType,
    QuestionCategory,
    PatternConfidenceCalculator,
    ProgressiveQuestionEngine,
    ConversationManager,
    DecisionEngine,
    BrainstormingEngine,
    get_question_by_id,
    get_questions_by_category,
    MOCK_QUESTION_DATABASE
)
from backend.src.integrations.llm_client import LLMClient
from backend.src.integrations.github_client import GitHubClient


class TestDataModels:
    """Test the core data models"""
    
    def test_questionnaire_response_creation(self):
        """Test QuestionnaireResponse creation and serialization"""
        response = QuestionnaireResponse(
            question_id="test_q",
            question_text="Test question?",
            answer="Test answer",
            confidence=0.8
        )
        
        assert response.question_id == "test_q"
        assert response.question_text == "Test question?"
        assert response.answer == "Test answer"
        assert response.confidence == 0.8
        assert isinstance(response.timestamp, datetime)
        
        # Test serialization
        data = response.to_dict()
        assert data["question_id"] == "test_q"
        assert data["confidence"] == 0.8
        assert "timestamp" in data
    
    def test_pattern_confidence_creation(self):
        """Test PatternConfidence creation and serialization"""
        pattern_conf = PatternConfidence(
            pattern_name="rest_api",
            confidence=0.85,
            reasoning="Strong API indicators",
            contributing_factors=["communication_style: Web API (+0.9)"]
        )
        
        assert pattern_conf.pattern_name == "rest_api"
        assert pattern_conf.confidence == 0.85
        assert "API indicators" in pattern_conf.reasoning
        assert len(pattern_conf.contributing_factors) == 1
        
        # Test serialization
        data = pattern_conf.to_dict()
        assert data["pattern_name"] == "rest_api"
        assert data["confidence"] == 0.85
    
    def test_intelligent_decision_creation(self):
        """Test IntelligentDecision creation and serialization"""
        decision = IntelligentDecision(
            decision_type="database",
            recommendation="PostgreSQL",
            confidence=0.87,
            reasoning="Best for scalable applications",
            alternatives=[{"name": "MongoDB", "score": 0.75}],
            health_metrics={"stars": 15000}
        )
        
        assert decision.decision_type == "database"
        assert decision.recommendation == "PostgreSQL"
        assert decision.confidence == 0.87
        assert len(decision.alternatives) == 1
        
        # Test serialization
        data = decision.to_dict()
        assert data["recommendation"] == "PostgreSQL"
        assert data["health_metrics"]["stars"] == 15000
    
    def test_brainstorming_session_creation(self):
        """Test BrainstormingSession creation and methods"""
        session = BrainstormingSession(
            session_id="test_session",
            project_description="Test project"
        )
        
        assert session.session_id == "test_session"
        assert session.project_description == "Test project"
        assert len(session.responses) == 0
        assert not session.conversation_complete
        
        # Test adding response
        response = QuestionnaireResponse(
            question_id="test_q",
            question_text="Test?",
            answer="Yes"
        )
        session.add_response(response)
        
        assert len(session.responses) == 1
        assert session.has_answered_question("test_q")
        assert not session.has_answered_question("other_q")
        
        # Test getting response
        retrieved = session.get_response_by_question_id("test_q")
        assert retrieved is not None
        assert retrieved.answer == "Yes"
        
        # Test serialization
        data = session.to_dict()
        assert data["session_id"] == "test_session"
        assert len(data["responses"]) == 1


class TestQuestionDatabase:
    """Test the question database functionality"""
    
    def test_get_question_by_id(self):
        """Test retrieving questions by ID"""
        question = get_question_by_id("project_description")
        assert question is not None
        assert question.id == "project_description"
        assert question.question_type == QuestionType.TEXT
        
        # Test non-existent question
        assert get_question_by_id("nonexistent") is None
    
    def test_get_questions_by_category(self):
        """Test retrieving questions by category"""
        identity_questions = get_questions_by_category(QuestionCategory.PROJECT_IDENTITY)
        assert len(identity_questions) >= 2
        
        for question in identity_questions:
            assert question.category == QuestionCategory.PROJECT_IDENTITY
    
    def test_question_database_completeness(self):
        """Test that question database has required questions"""
        required_questions = ["project_description", "deployment_target", "communication_style"]
        
        for question_id in required_questions:
            question = get_question_by_id(question_id)
            assert question is not None, f"Missing required question: {question_id}"


class TestPatternConfidenceCalculator:
    """Test pattern confidence calculation"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.calculator = PatternConfidenceCalculator()
    
    def test_calculate_pattern_scores_empty(self):
        """Test pattern scoring with no responses"""
        scores = self.calculator.calculate_pattern_scores([])
        
        # Should return scores for all patterns
        assert len(scores) > 0
        
        # All scores should be 0.0 with no responses
        for pattern_name, pattern_conf in scores.items():
            assert pattern_conf.confidence == 0.0
            assert pattern_conf.pattern_name == pattern_name
    
    def test_calculate_pattern_scores_api_project(self):
        """Test pattern scoring for API project"""
        responses = [
            QuestionnaireResponse(
                question_id="communication_style",
                question_text="How will users interact?",
                answer="Web API (REST/GraphQL)"
            ),
            QuestionnaireResponse(
                question_id="deployment_target",
                question_text="Where will it run?",
                answer="Cloud Server / Virtual Machine"
            )
        ]
        
        scores = self.calculator.calculate_pattern_scores(responses)
        
        # REST API pattern should have high confidence
        rest_api_score = scores.get("rest_api")
        assert rest_api_score is not None
        assert rest_api_score.confidence > 0.5
        assert "Web API" in rest_api_score.reasoning or len(rest_api_score.contributing_factors) > 0
    
    def test_get_recommended_patterns(self):
        """Test getting recommended patterns above threshold"""
        pattern_scores = {
            "rest_api": PatternConfidence("rest_api", 0.85, "High confidence"),
            "api_gateway": PatternConfidence("api_gateway", 0.65, "Medium confidence"),
            "message_queue": PatternConfidence("message_queue", 0.45, "Low confidence")
        }
        
        # Test with default threshold (0.6)
        recommended = self.calculator.get_recommended_patterns(pattern_scores)
        assert len(recommended) == 2
        assert "rest_api" in recommended
        assert "api_gateway" in recommended
        assert "message_queue" not in recommended
        
        # Test with higher threshold
        recommended_high = self.calculator.get_recommended_patterns(pattern_scores, threshold=0.8)
        assert len(recommended_high) == 1
        assert recommended_high[0] == "rest_api"


class TestProgressiveQuestionEngine:
    """Test progressive question selection"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.mock_llm = AsyncMock(spec=LLMClient)
        self.engine = ProgressiveQuestionEngine(self.mock_llm)
    
    @pytest.mark.asyncio
    async def test_get_next_questions_initial(self):
        """Test getting initial questions for new session"""
        session = BrainstormingSession(session_id="test")
        
        questions = await self.engine.get_next_questions(session, max_questions=3)
        
        assert len(questions) <= 3
        assert len(questions) > 0
        
        # Should return required questions first
        for question in questions:
            assert question.required
    
    @pytest.mark.asyncio
    async def test_get_next_questions_with_responses(self):
        """Test getting questions after some responses"""
        session = BrainstormingSession(session_id="test")
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))
        
        questions = await self.engine.get_next_questions(session, max_questions=3)
        
        assert len(questions) <= 3
        # Should not include already answered questions
        question_ids = [q.id for q in questions]
        assert "communication_style" not in question_ids
    
    @pytest.mark.asyncio
    async def test_conditional_questions(self):
        """Test conditional question triggering"""
        session = BrainstormingSession(session_id="test")
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))
        
        conditional_questions = await self.engine._get_conditional_questions(session)
        
        # Should trigger authentication question for API projects
        auth_questions = [q for q in conditional_questions if "auth" in q.id.lower()]
        assert len(auth_questions) > 0


class TestConversationManager:
    """Test LLM conversation management"""

    def setup_method(self):
        """Set up test fixtures"""
        self.mock_llm = AsyncMock(spec=LLMClient)
        self.manager = ConversationManager(self.mock_llm)

    @pytest.mark.asyncio
    async def test_build_conversation_context(self):
        """Test building conversation context"""
        session = BrainstormingSession(
            session_id="test",
            project_description="A blog API"
        )
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))

        context = await self.manager.build_conversation_context(session)

        assert "A blog API" in context
        assert "Web API" in context
        assert "How will users interact?" in context

    @pytest.mark.asyncio
    async def test_get_intelligent_follow_up(self):
        """Test getting intelligent follow-up analysis"""
        session = BrainstormingSession(session_id="test")
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))

        follow_up = await self.manager.get_intelligent_follow_up(session)

        assert "confidence" in follow_up
        assert "reasoning" in follow_up
        assert "assumptions" in follow_up
        assert isinstance(follow_up["next_questions"], list)

    @pytest.mark.asyncio
    async def test_generate_confident_assumptions(self):
        """Test generating confident assumptions"""
        session = BrainstormingSession(session_id="test")
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))

        assumptions = await self.manager.generate_confident_assumptions(session)

        assert isinstance(assumptions, list)
        if assumptions:  # May be empty for minimal responses
            assumption = assumptions[0]
            assert "assumption" in assumption
            assert "confidence" in assumption
            assert "reasoning" in assumption
            assert assumption["confidence"] > 0.0


class TestDecisionEngine:
    """Test intelligent decision making"""

    def setup_method(self):
        """Set up test fixtures"""
        self.mock_github = AsyncMock(spec=GitHubClient)
        self.mock_llm = AsyncMock(spec=LLMClient)
        self.engine = DecisionEngine(self.mock_github, self.mock_llm)

    @pytest.mark.asyncio
    async def test_make_database_decision(self):
        """Test database selection decision"""
        context = {
            "expected_scale": "High (10,000+, requires significant scalability)",
            "data_persistence": "yes"
        }

        decision = await self.engine.make_intelligent_decision("database", context)

        assert decision.decision_type == "database"
        assert decision.recommendation in ["PostgreSQL", "MongoDB", "Redis"]
        assert decision.confidence > 0.0
        assert len(decision.reasoning) > 0
        assert isinstance(decision.alternatives, list)
        assert isinstance(decision.health_metrics, dict)

    @pytest.mark.asyncio
    async def test_make_web_framework_decision(self):
        """Test web framework selection decision"""
        context = {"communication_style": "Web API (REST/GraphQL)"}

        decision = await self.engine.make_intelligent_decision("web_framework", context)

        assert decision.decision_type == "web_framework"
        assert decision.recommendation == "FastAPI"
        assert decision.confidence > 0.8
        assert "FastAPI" in decision.reasoning

    @pytest.mark.asyncio
    async def test_make_authentication_decision(self):
        """Test authentication method decision"""
        context = {"expected_scale": "Medium (100s-1000s, startup/small business)"}

        decision = await self.engine.make_intelligent_decision("authentication", context)

        assert decision.decision_type == "authentication"
        assert "JWT" in decision.recommendation or "OAuth2" in decision.recommendation
        assert decision.confidence > 0.0

    @pytest.mark.asyncio
    async def test_unknown_decision_type(self):
        """Test handling unknown decision types"""
        decision = await self.engine.make_intelligent_decision("unknown_type", {})

        assert decision.decision_type == "unknown_type"
        assert decision.confidence == 0.0
        assert "Unknown decision type" in decision.reasoning


class TestBrainstormingEngine:
    """Test the main brainstorming orchestrator"""

    def setup_method(self):
        """Set up test fixtures"""
        self.mock_llm = AsyncMock(spec=LLMClient)
        self.mock_github = AsyncMock(spec=GitHubClient)
        self.engine = BrainstormingEngine(self.mock_llm, self.mock_github)

    @pytest.mark.asyncio
    async def test_start_brainstorming(self):
        """Test starting a new brainstorming session"""
        session = await self.engine.start_brainstorming(
            session_id="test_session",
            initial_description="A blog API project"
        )

        assert session.session_id == "test_session"
        assert session.project_description == "A blog API project"
        assert len(session.next_questions) > 0
        assert not session.conversation_complete

    @pytest.mark.asyncio
    async def test_process_answer(self):
        """Test processing a user answer"""
        session = BrainstormingSession(session_id="test")

        updated_session = await self.engine.process_answer(
            session=session,
            question_id="communication_style",
            answer="Web API (REST/GraphQL)",
            confidence=0.9
        )

        assert len(updated_session.responses) == 1
        assert updated_session.responses[0].answer == "Web API (REST/GraphQL)"
        assert updated_session.responses[0].confidence == 0.9

        # Should have updated pattern scores
        assert len(updated_session.pattern_scores) > 0

        # Should have next questions
        assert len(updated_session.next_questions) >= 0

    @pytest.mark.asyncio
    async def test_process_invalid_question(self):
        """Test processing answer for invalid question"""
        session = BrainstormingSession(session_id="test")

        with pytest.raises(ValueError, match="Unknown question ID"):
            await self.engine.process_answer(
                session=session,
                question_id="invalid_question",
                answer="Some answer"
            )

    @pytest.mark.asyncio
    async def test_conversation_completion(self):
        """Test conversation completion logic"""
        session = BrainstormingSession(session_id="test")

        # Add enough responses to potentially complete conversation
        required_responses = [
            ("project_description", "A blog API"),
            ("communication_style", "Web API (REST/GraphQL)"),
            ("deployment_target", "Cloud Server / Virtual Machine"),
            ("expected_scale", "Medium (100s-1000s, startup/small business)"),
            ("data_persistence", "yes")
        ]

        for question_id, answer in required_responses:
            session = await self.engine.process_answer(session, question_id, answer)

        # Should eventually complete or be close to completion
        assert len(session.responses) == len(required_responses)
        assert len(session.pattern_scores) > 0

        # Check if any patterns have reasonable confidence
        has_confident_pattern = any(
            pattern_conf.confidence >= 0.5
            for pattern_conf in session.pattern_scores.values()
        )
        assert has_confident_pattern

    @pytest.mark.asyncio
    async def test_get_session_status(self):
        """Test getting session status"""
        session = BrainstormingSession(session_id="test")
        session.add_response(QuestionnaireResponse(
            question_id="communication_style",
            question_text="How will users interact?",
            answer="Web API (REST/GraphQL)"
        ))

        status = await self.engine.get_session_status(session)

        assert status["session_id"] == "test"
        assert status["responses_count"] == 1
        assert "progress" in status
        assert "patterns_identified" in status
        assert "decisions_made" in status
        assert "conversation_complete" in status
        assert "last_updated" in status

        # Progress should be between 0 and 1
        assert 0 <= status["progress"] <= 1


class TestIntegration:
    """Integration tests for the complete brainstorming flow"""

    def setup_method(self):
        """Set up test fixtures"""
        self.mock_llm = AsyncMock(spec=LLMClient)
        self.mock_github = AsyncMock(spec=GitHubClient)
        self.engine = BrainstormingEngine(self.mock_llm, self.mock_github)

    @pytest.mark.asyncio
    async def test_complete_brainstorming_flow(self):
        """Test a complete brainstorming session from start to finish"""
        # Start session
        session = await self.engine.start_brainstorming(
            session_id="integration_test",
            initial_description="A REST API for a blog platform"
        )

        # Simulate answering questions
        question_answers = [
            ("project_description", "A REST API for a blog platform"),
            ("communication_style", "Web API (REST/GraphQL)"),
            ("deployment_target", "Cloud Server / Virtual Machine"),
            ("expected_scale", "Medium (100s-1000s, startup/small business)"),
            ("data_persistence", "yes")
        ]

        for question_id, answer in question_answers:
            if question_id in [q.id for q in MOCK_QUESTION_DATABASE.values()]:
                session = await self.engine.process_answer(session, question_id, answer)

        # Verify final state
        assert len(session.responses) == len(question_answers)
        assert len(session.pattern_scores) > 0

        # Should have identified REST API pattern with high confidence
        rest_api_confidence = session.pattern_scores.get("rest_api")
        if rest_api_confidence:
            assert rest_api_confidence.confidence > 0.5

        # Get final status
        status = await self.engine.get_session_status(session)
        assert status["responses_count"] == len(question_answers)
        assert status["patterns_identified"] > 0
