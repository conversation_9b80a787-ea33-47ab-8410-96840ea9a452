#!/usr/bin/env python3
"""
Enhanced Brainstorming API Test with Real LLM Integration.

This script demonstrates how to test the brainstorming system with your real LLM APIs.
It provides an interactive CLI interface for testing the complete brainstorming flow.
"""

import asyncio
import requests
import json
import sys
from pathlib import Path

# Add backend to path for imports
sys.path.append(str(Path(__file__).parent / "backend" / "src"))

BASE_URL = "http://localhost:8000"


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🧠 {title}")
    print(f"{'='*60}")


def print_section(title):
    """Print a formatted section"""
    print(f"\n{'-'*40}")
    print(f"📋 {title}")
    print(f"{'-'*40}")


def test_server_connection():
    """Test if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start the server:")
        print("   uvicorn backend.src.main:app --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        print(f"❌ Server connection error: {e}")
        return False


def interactive_brainstorming_test():
    """Interactive brainstorming test with real user input"""
    
    print_header("Interactive Brainstorming Test")
    
    # Check server
    if not test_server_connection():
        return
    
    session_id = input("\n🆔 Enter a session ID (or press Enter for 'interactive_test'): ").strip()
    if not session_id:
        session_id = "interactive_test"
    
    project_description = input("📝 Describe your project idea: ").strip()
    if not project_description:
        project_description = "A REST API for a blog platform with user authentication"
    
    print_section("Starting Brainstorming Session")
    
    # 1. Start session
    try:
        response = requests.post(
            f"{BASE_URL}/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": project_description},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session started: {session_id}")
            print(f"📝 Project: {data['project_description']}")
            print(f"❓ Next questions available: {len(data['next_questions'])}")
        else:
            print(f"❌ Failed to start session: {response.status_code}")
            print(response.text)
            return
            
    except Exception as e:
        print(f"❌ Error starting session: {e}")
        return
    
    # 2. Interactive question answering
    print_section("Question & Answer Phase")
    
    # Get available questions
    try:
        response = requests.get(f"{BASE_URL}/api/projects/{session_id}/brainstorm/questions")
        if response.status_code == 200:
            questions = response.json()
            print(f"📋 Available questions: {len(questions)}")
        else:
            print("⚠️  Could not fetch questions, continuing with manual input")
            questions = []
    except Exception as e:
        print(f"⚠️  Error fetching questions: {e}")
        questions = []
    
    # Show available questions
    if questions:
        print("\n🔍 Available Questions:")
        for i, q in enumerate(questions[:5], 1):
            print(f"   {i}. {q['id']}: {q['text']}")
            if q['options']:
                for opt in q['options'][:3]:
                    print(f"      - {opt}")
                if len(q['options']) > 3:
                    print(f"      ... and {len(q['options']) - 3} more options")
    
    # Interactive answering
    answered_questions = []
    
    while True:
        print(f"\n📝 Answer Questions (answered: {len(answered_questions)})")
        
        if questions:
            question_id = input("Enter question ID (or 'status' to check progress, 'done' to finish): ").strip()
        else:
            question_id = input("Enter question ID manually (or 'status', 'done'): ").strip()
        
        if question_id.lower() == 'done':
            break
        elif question_id.lower() == 'status':
            # Get session status
            try:
                response = requests.get(f"{BASE_URL}/api/projects/{session_id}/brainstorm/status")
                if response.status_code == 200:
                    status = response.json()
                    print(f"\n📊 Session Status:")
                    print(f"   Progress: {status['progress']:.1%}")
                    print(f"   Responses: {status['responses_count']}")
                    print(f"   Patterns identified: {status['patterns_identified']}")
                    print(f"   Decisions made: {status['decisions_made']}")
                    print(f"   Complete: {status['conversation_complete']}")
                else:
                    print(f"❌ Status check failed: {response.status_code}")
            except Exception as e:
                print(f"❌ Status error: {e}")
            continue
        
        if not question_id:
            continue
        
        # Find question details
        question_info = None
        for q in questions:
            if q['id'] == question_id:
                question_info = q
                break
        
        if question_info:
            print(f"\n❓ {question_info['text']}")
            if question_info['options']:
                print("   Options:")
                for i, opt in enumerate(question_info['options'], 1):
                    print(f"   {i}. {opt}")
        
        answer = input("Your answer: ").strip()
        if not answer:
            continue
        
        confidence = input("Confidence (0.0-1.0, default 0.9): ").strip()
        try:
            confidence = float(confidence) if confidence else 0.9
        except ValueError:
            confidence = 0.9
        
        # Submit answer
        try:
            response = requests.post(
                f"{BASE_URL}/api/projects/{session_id}/brainstorm/answer",
                json={
                    "question_id": question_id,
                    "answer": answer,
                    "confidence": confidence
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Answer recorded!")
                
                # Show pattern confidences
                if data['pattern_confidences']:
                    print(f"🎯 Pattern Confidences:")
                    for pattern in data['pattern_confidences'][:3]:
                        print(f"   - {pattern['pattern_name']}: {pattern['confidence']:.2f}")
                
                # Show intelligent decisions
                if data['intelligent_decisions']:
                    print(f"🤖 Intelligent Decisions:")
                    for decision in data['intelligent_decisions']:
                        print(f"   - {decision['recommendation']} for {decision['decision_type']} ({decision['confidence']:.2f})")
                
                answered_questions.append(question_id)
                
                if data['conversation_complete']:
                    print("🎉 Conversation marked as complete!")
                    break
                    
            else:
                print(f"❌ Failed to submit answer: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"❌ Error submitting answer: {e}")
    
    # 3. Complete brainstorming
    print_section("Completing Brainstorming")
    
    try:
        response = requests.post(f"{BASE_URL}/api/projects/{session_id}/brainstorm/complete")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Brainstorming completed!")
            
            print(f"\n📋 Project Overview:")
            print(f"   Description: {data['project_overview']['description']}")
            print(f"   Total responses: {data['project_overview']['total_responses']}")
            
            print(f"\n🏗️  Recommended Patterns ({len(data['recommended_patterns'])}):")
            for pattern in data['recommended_patterns'][:5]:
                print(f"   - {pattern['name']}: {pattern['confidence']:.2f}")
                print(f"     {pattern['reasoning'][:100]}...")
            
            print(f"\n🤖 Intelligent Decisions ({len(data['intelligent_decisions'])}):")
            for decision in data['intelligent_decisions']:
                print(f"   - {decision['recommendation']} for {decision['decision_type']}")
                print(f"     Confidence: {decision['confidence']:.2f}")
                print(f"     Reasoning: {decision['reasoning'][:100]}...")
            
            print(f"\n📝 Next Steps:")
            for step in data['next_steps']:
                print(f"   - {step}")
            
            # Save detailed results
            with open(f"brainstorming_results_{session_id}.json", "w") as f:
                json.dump(data, f, indent=2)
            print(f"\n💾 Detailed results saved to: brainstorming_results_{session_id}.json")
            
        else:
            print(f"❌ Failed to complete brainstorming: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error completing brainstorming: {e}")
    
    # 4. Cleanup
    cleanup = input("\n🗑️  Delete session? (y/N): ").strip().lower()
    if cleanup == 'y':
        try:
            response = requests.delete(f"{BASE_URL}/api/projects/{session_id}/brainstorm")
            if response.status_code == 200:
                print("✅ Session deleted")
            else:
                print(f"⚠️  Delete failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️  Delete error: {e}")
    
    print("\n🎉 Interactive brainstorming test completed!")


def automated_demo():
    """Run an automated demo with predefined answers"""
    
    print_header("Automated Demo with Real LLM")
    
    if not test_server_connection():
        return
    
    session_id = "automated_demo"
    
    # Predefined demo flow
    demo_data = {
        "description": "A social media platform for developers to share code snippets",
        "answers": [
            ("project_description", "A social media platform for developers to share code snippets"),
            ("communication_style", "Web API (REST/GraphQL)"),
            ("deployment_target", "Cloud Server / Virtual Machine"),
            ("expected_scale", "High (10,000+, requires significant scalability)"),
            ("data_persistence", "yes")
        ]
    }
    
    print(f"🎬 Running automated demo: {demo_data['description']}")
    
    # Start session
    response = requests.post(
        f"{BASE_URL}/api/projects/{session_id}/brainstorm/start",
        json={"initial_description": demo_data['description']}
    )
    
    if response.status_code != 200:
        print(f"❌ Demo failed to start: {response.status_code}")
        return
    
    print("✅ Demo session started")
    
    # Submit answers
    for question_id, answer in demo_data['answers']:
        print(f"📝 Answering: {question_id} = {answer}")
        
        response = requests.post(
            f"{BASE_URL}/api/projects/{session_id}/brainstorm/answer",
            json={"question_id": question_id, "answer": answer, "confidence": 0.9}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['pattern_confidences']:
                top_pattern = max(data['pattern_confidences'], key=lambda x: x['confidence'])
                print(f"   🎯 Top pattern: {top_pattern['pattern_name']} ({top_pattern['confidence']:.2f})")
        else:
            print(f"   ⚠️  Answer failed: {response.status_code}")
    
    # Complete and show results
    response = requests.post(f"{BASE_URL}/api/projects/{session_id}/brainstorm/complete")
    if response.status_code == 200:
        data = response.json()
        print(f"\n🎉 Demo completed!")
        print(f"   Patterns: {len(data['recommended_patterns'])}")
        print(f"   Decisions: {len(data['intelligent_decisions'])}")
        
        # Cleanup
        requests.delete(f"{BASE_URL}/api/projects/{session_id}/brainstorm")
        print("   ✅ Demo session cleaned up")
    else:
        print(f"❌ Demo completion failed: {response.status_code}")


if __name__ == "__main__":
    print("🧠 CodeQuilter Brainstorming Test Suite")
    print("Choose your testing mode:")
    print("1. Interactive Test (answer questions manually)")
    print("2. Automated Demo (predefined answers)")
    print("3. Both")
    
    choice = input("\nEnter choice (1/2/3): ").strip()
    
    if choice == "1":
        interactive_brainstorming_test()
    elif choice == "2":
        automated_demo()
    elif choice == "3":
        automated_demo()
        interactive_brainstorming_test()
    else:
        print("Invalid choice. Running interactive test...")
        interactive_brainstorming_test()
